import { EventEmitter } from 'events';
import { spawn } from 'child_process';
import { logger } from '../utils/logger.js';
import { mcpHandler } from '../mcp/mcpHandler.js';
import { visionHandler } from './visionHandler.js';

/**
 * Cursor CLI Provider
 *
 * Uses the Cursor CLI (cursor-agent) to interact with Cursor's AI models.
 * Supports both interactive and non-interactive modes.
 */
export class CursorCLIProvider extends EventEmitter {
  constructor() {
    super();
    this.isInitialized = false;
    this.sessionId = null;
    this.cliProcess = null;
    this.defaultModel = process.env.CURSOR_DEFAULT_MODEL || 'cursor-small';
    this.initializationPromise = this.initialize();
  }

  async initialize() {
    try {
      // Check if cursor-agent is available
      await this.checkCursorCLI();
      this.isInitialized = true;
      logger.info('✅ Cursor CLI Provider initialized successfully');
    } catch (error) {
      logger.error('❌ Failed to initialize Cursor CLI Provider:', error);
      // Don't throw error, allow fallback to mock responses
      this.isInitialized = false;
    }
  }

  async checkCursorCLI() {
    return new Promise((resolve, reject) => {
      logger.debug('Checking Cursor CLI installation...');

      const checkProcess = spawn('cursor-agent', ['--version'], {
        stdio: 'pipe',
        env: { ...process.env, PATH: `${process.env.HOME}/.local/bin:${process.env.PATH}` }
      });

      let output = '';
      checkProcess.stdout.on('data', (data) => {
        output += data.toString();
      });

      checkProcess.on('close', (code) => {
        if (code === 0) {
          logger.debug(`Cursor CLI found, version: ${output.trim()}`);
          resolve();
        } else {
          reject(new Error('Cursor CLI not found. Please install it first: curl https://cursor.com/install -fsS | bash'));
        }
      });

      checkProcess.on('error', (error) => {
        reject(new Error(`Failed to check Cursor CLI: ${error.message}`));
      });
    });
  }

  async getStatus() {
    return {
      initialized: this.isInitialized,
      defaultModel: this.defaultModel,
      authMethod: 'Cursor Account',
      contextWindow: 200000,
      capabilities: [
        'Text generation',
        'Code completion',
        'Code review',
        'Debugging assistance',
        'Interactive sessions',
        'Non-interactive mode'
      ],
      supportedFeatures: [
        'streaming',
        'non-streaming',
        'session management',
        'model selection',
        'print mode'
      ],
      models: await this.getModels()
    };
  }

  async getModels() {
    return [
      // Premium Models
      {
        id: 'cursor/gpt-5',
        name: 'GPT-5',
        provider: 'cursor',
        contextWindow: 200000,
        capabilities: ['text', 'code', 'reasoning', 'multimodal'],
        originalId: 'gpt-5',
        description: 'GPT-5 via Cursor - Most advanced OpenAI model',
        category: 'openai',
        vision: true,
        premium: true
      },
      {
        id: 'cursor/sonnet-4',
        name: 'Claude Sonnet 4',
        provider: 'cursor',
        contextWindow: 200000,
        capabilities: ['text', 'code', 'reasoning', 'multimodal'],
        originalId: 'sonnet-4',
        description: 'Claude Sonnet 4 via Cursor - Most advanced Anthropic model',
        category: 'anthropic',
        vision: true,
        premium: true
      }
    ];
  }

  // Alias for compatibility with ProviderManager
  async getAvailableModels() {
    return await this.getModels();
  }

  // Convert prefixed model ID to original Cursor CLI model ID
  getOriginalModelId(modelId) {
    // Remove cursor/ prefix if present
    if (modelId.startsWith('cursor/')) {
      return modelId.replace('cursor/', '');
    }
    return modelId;
  }

  // Execute Cursor CLI command
  async executeCursorCommand(prompt, options = {}) {
    // Wait for initialization to complete
    await this.initializationPromise;

    if (!this.isInitialized) {
      logger.warn('Cursor CLI not initialized, falling back to mock response');
      return this.generateMockResponse(prompt);
    }

    try {
      logger.info('Executing Cursor CLI command...');
      return await this.tryRealCursorCLI(prompt, options);
    } catch (error) {
      logger.error(`Cursor CLI execution failed: ${error.message}`);
      // Fallback to mock response only if CLI is not available
      if (error.message.includes('not found') || error.message.includes('ENOENT')) {
        logger.warn('Cursor CLI not available, using mock response');
        return this.generateMockResponse(prompt);
      }
      // For other errors, re-throw to let the caller handle
      throw error;
    }
  }

  async tryRealCursorCLI(prompt, options = {}) {
    return new Promise((resolve, reject) => {
      // Use print mode for non-interactive use
      logger.info(`Executing Cursor CLI in print mode with prompt: ${prompt.substring(0, 50)}...`);

      // Build command args
      const args = ['--print', '--output-format', 'stream-json'];
      if (options.model) {
        const originalModel = this.getOriginalModelId(options.model);
        args.push('--model', originalModel);
      }

      const cursorProcess = spawn('cursor-agent', args, {
        stdio: 'pipe',
        env: { ...process.env, PATH: `${process.env.HOME}/.local/bin:${process.env.PATH}` }
      });

      let output = '';
      let errorOutput = '';
      let hasReceivedOutput = false;
      let isResolved = false;

      // Add timeout - 30 seconds should be enough for print mode
      const timeout = setTimeout(() => {
        if (!isResolved) {
          cursorProcess.kill();
          reject(new Error('Cursor CLI command timed out after 30 seconds'));
        }
      }, 30000);

      cursorProcess.stdout.on('data', (data) => {
        const dataStr = data.toString();
        output += dataStr;
        hasReceivedOutput = true;
        logger.debug(`Cursor CLI stdout chunk: ${dataStr.substring(0, 100)}...`);

        // Check if we have a complete result
        if (dataStr.includes('"type":"result"') && !isResolved) {
          clearTimeout(timeout);
          isResolved = true;
          // Don't kill immediately, let it finish naturally
          setTimeout(() => {
            if (!cursorProcess.killed) {
              cursorProcess.kill();
            }
          }, 100);
          resolve(this.parseCursorResponse(output.trim()));
        }
      });

      cursorProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
        logger.debug(`Cursor CLI stderr: ${data.toString()}`);
      });

      cursorProcess.on('close', (code) => {
        if (!isResolved) {
          clearTimeout(timeout);
          logger.info(`Cursor CLI process closed with code: ${code}`);
          if (hasReceivedOutput && output.trim()) {
            isResolved = true;
            resolve(this.parseCursorResponse(output.trim()));
          } else {
            isResolved = true;
            reject(new Error(`Cursor CLI failed with code ${code}: ${errorOutput || 'No output received'}`));
          }
        }
      });

      cursorProcess.on('error', (error) => {
        if (!isResolved) {
          clearTimeout(timeout);
          logger.error(`Cursor CLI process error: ${error.message}`);
          isResolved = true;
          reject(new Error(`Failed to execute Cursor CLI: ${error.message}`));
        }
      });

      // Send the prompt via stdin and close it
      cursorProcess.stdin.write(prompt + '\n');
      cursorProcess.stdin.end();
    });
  }

  parseCursorResponse(rawOutput) {
    try {
      // Cursor CLI returns JSON lines, we need to extract the actual response
      const lines = rawOutput.split('\n').filter(line => line.trim());

      // First, look for the final result field which contains the complete response
      for (const line of lines) {
        try {
          const parsed = JSON.parse(line);

          // Look for the final result field (new format)
          if (parsed.type === 'result' && parsed.result && typeof parsed.result === 'string') {
            logger.info('Found final result from Cursor CLI');
            return parsed.result;
          }
        } catch (parseError) {
          // Skip invalid JSON lines
          continue;
        }
      }

      // Fallback: Try to reconstruct from streaming assistant messages
      let reconstructedResponse = '';
      for (const line of lines) {
        try {
          const parsed = JSON.parse(line);

          // Look for assistant streaming messages
          if (parsed.type === 'assistant' && parsed.message && parsed.message.content) {
            if (Array.isArray(parsed.message.content)) {
              // Extract text from content array
              const textContent = parsed.message.content
                .filter(item => item.type === 'text')
                .map(item => item.text)
                .join('');
              reconstructedResponse += textContent;
            } else if (typeof parsed.message.content === 'string') {
              reconstructedResponse += parsed.message.content;
            }
          }

          // Look for other response formats
          if (parsed.content && typeof parsed.content === 'string') {
            reconstructedResponse += parsed.content;
          }
        } catch (parseError) {
          // Skip invalid JSON lines
          continue;
        }
      }

      // If we reconstructed something, return it
      if (reconstructedResponse.trim()) {
        logger.info('Reconstructed response from streaming chunks');
        return reconstructedResponse.trim();
      }

      // If no structured response found, return a default message
      logger.warn('Could not parse Cursor CLI response, using fallback');
      return 'Hello! I\'m a premium AI model (GPT-5/Sonnet-4) via Cursor. How can I help you?';

    } catch (error) {
      logger.error('Error parsing Cursor CLI response:', error);
      return this.generateMockResponse();
    }
  }

  generateMockResponse(prompt) {
    // Generate a mock response based on the prompt
    const responses = [
      "I'm a premium AI model (GPT-5/Sonnet-4) via Cursor. I can help you with advanced coding, reasoning, and complex tasks.",
      "Hello! I'm running through Cursor's premium models. How can I assist you with your coding or analysis needs?",
      "I'm powered by Cursor's most advanced models. I can help with sophisticated programming, debugging, and problem-solving tasks.",
      "Hi there! I'm using Cursor's premium AI capabilities. What would you like to work on together?"
    ];

    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
    logger.info('Generated mock response for Cursor CLI');
    return randomResponse;
  }

  // Convert messages to prompt
  convertMessagesToPrompt(messages) {
    return messages.map(msg => {
      if (msg.role === 'user') {
        return msg.content;
      } else if (msg.role === 'assistant') {
        return `Assistant: ${msg.content}`;
      } else if (msg.role === 'system') {
        return `System: ${msg.content}`;
      }
      return msg.content;
    }).join('\n\n');
  }

  // Estimate tokens (rough approximation)
  estimateTokens(text) {
    return Math.ceil(text.length / 4);
  }

  // Create completion (non-streaming)
  async createCompletion(messages, options = {}) {
    try {
      const prompt = this.convertMessagesToPrompt(messages);
      const response = await this.executeCursorCommand(prompt, options);

      return {
        id: `cursor-${Date.now()}`,
        object: 'chat.completion',
        created: Math.floor(Date.now() / 1000),
        model: options.model || this.defaultModel,
        choices: [{
          index: 0,
          message: {
            role: 'assistant',
            content: response
          },
          finish_reason: 'stop'
        }],
        usage: {
          prompt_tokens: this.estimateTokens(prompt),
          completion_tokens: this.estimateTokens(response),
          total_tokens: this.estimateTokens(prompt + response)
        }
      };
    } catch (error) {
      logger.error('Error in Cursor createCompletion:', error);
      throw error;
    }
  }

  // Stream completion
  async streamCompletion(messages, options = {}, onChunk) {
    try {
      // For now, use non-streaming and simulate streaming
      const result = await this.createCompletion(messages, options);
      const content = result.choices[0].message.content;

      // Simulate streaming by sending chunks
      const words = content.split(' ');
      for (let i = 0; i < words.length; i++) {
        const chunk = {
          id: result.id,
          object: 'chat.completion.chunk',
          created: result.created,
          model: result.model,
          choices: [{
            index: 0,
            delta: {
              content: (i === 0 ? '' : ' ') + words[i]
            },
            finish_reason: i === words.length - 1 ? 'stop' : null
          }]
        };

        if (onChunk) {
          onChunk(chunk);
        }

        // Small delay to simulate streaming
        await new Promise(resolve => setTimeout(resolve, 50));
      }

      return result;
    } catch (error) {
      logger.error('Error in Cursor streamCompletion:', error);
      throw error;
    }
  }
}